import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from mcp_oracle_server import app

client = TestClient(app)

# 模拟数据库连接
@pytest.fixture
def mock_connection():
    with patch("mcp_oracle_server.get_connection") as mock_conn:
        # 创建模拟的连接和游标
        conn = MagicMock()
        cursor = MagicMock()
        conn.cursor.return_value = cursor
        mock_conn.return_value = conn
        yield conn, cursor

def test_get_database_info(mock_connection):
    conn, cursor = mock_connection
    cursor.fetchone.return_value = ["TEST_DB"]
    
    response = client.get("/databases")
    
    assert response.status_code == 200
    assert response.json() == {"name": "TEST_DB"}
    cursor.execute.assert_called_with("SELECT name FROM v$database")

def test_get_tables(mock_connection):
    conn, cursor = mock_connection
    cursor.fetchall.return_value = [("EMPLOYEES", "TABLE"), ("DEPARTMENTS", "TABLE")]
    
    response = client.get("/tables?schema=HR")
    
    assert response.status_code == 200
    assert "tables" in response.json()