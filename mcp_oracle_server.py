import cx_<PERSON>
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel

app = FastAPI()

# 数据库连接配置（使用环境变量更安全）
import os

ORACLE_DSN = os.getenv("ORACLE_DSN", "localhost/XE")
ORACLE_USER = os.getenv("ORACLE_USER", "system")
ORACLE_PASSWORD = os.getenv("ORACLE_PASSWORD", "oracle")

import logging
logging.basicConfig(level=logging.INFO)

def get_connection():
    try:
        conn = cx_Oracle.connect(
            user=ORACLE_USER,
            password=ORACLE_PASSWORD,
            dsn=ORACLE_DSN
        )
        logging.info("Database connection established")
        return conn
    except Exception as e:
        logging.error(f"Connection failed: {str(e)}")
        raise

class QueryRequest(BaseModel):
    table_name: str

@app.get("/tables/{table_name}/columns")
async def get_table_columns(table_name: str):
    try:
        conn = get_connection()
        cursor = conn.cursor()
        cursor.prepare(
            "SELECT column_name, data_type, data_length "
            "FROM all_tab_cols "
            "WHERE table_name = :tbl_upper"
        )
        cursor.execute(None, {"tbl_upper": table_name.upper()})
        columns = [
            {"name": row[0], "type": row[1], "length": row[2]}
            for row in cursor
        ]
        return {"columns": columns}
    except cx_Oracle.DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        cursor.close()
        conn.close()

@app.get("/databases")
async def get_database_info():
    try:
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM v$database")
        db_name = cursor.fetchone()[0]
        return {"name": db_name}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        cursor.close()
        conn.close()

@app.get("/tables")
async def get_tables(schema: str = "USER"):
    try:
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute(
            "SELECT table_name, table_type FROM all_tables WHERE owner = :schema",
            schema=schema.upper(),
        )
        tables = [{"name": row[0], "type": row[1]} for row in cursor]
        return {"tables": tables}
    except cx_Oracle.DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        cursor.close()
        conn.close()

def validate_table(table_name):
    try:
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute(
            "SELECT COUNT(*) FROM all_tables WHERE table_name = UPPER(:tbl)",
            {"tbl": table_name},
        )
        exists = cursor.fetchone()[0] > 0
        return exists
    except Exception as e:
        raise HTTPException(500, detail=str(e))

@app.post("/tables/{table_name}/query")
async def query_table(table_name: str, limit: int = 10):
    if not validate_table(table_name.upper()):
        raise HTTPException(404, "Table does not exist")

    try:
        conn = get_connection()
        cursor = conn.cursor()
        cursor.prepare(
            f"SELECT * FROM {table_name.upper()} WHERE ROWNUM <= :limit"
        )
        cursor.execute(None, {"limit": limit})
        rows = [
            dict(zip([col[0] for col in cursor.description], row))
            for row in cursor
        ]
        return {"data": rows}
    except Exception as e:
        raise HTTPException(500, detail=str(e))
    finally:
        cursor.close()
        conn.close()

@app.post("/ddl")
async def execute_ddl(sql: str):
    allowed_dml = ["CREATE", "ALTER", "COMMENT"]
    if not any(sql.upper().startswith(k) for k in allowed_dml):
        raise HTTPException(403, "Only DDL operations allowed")

    forbidden_keywords = ["DROP", "TRUNCATE"]
    if any(kw in sql.upper() for kw in forbidden_keywords):
        raise HTTPException(403, "Forbidden SQL operation detected")

    try:
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute(sql)
        conn.commit()
        return {"status": "success", "sql_executed": sql}
    except Exception as e:
        conn.rollback()
        raise HTTPException(500, str(e))
    finally:
        cursor.close()
        conn.close()

@app.post("/tables/{table_name}/insert")
async def insert_data(table_name: str, data: dict):
    if not validate_table(table_name.upper()):
        raise HTTPException(404, "Table does not exist")

    columns = ", ".join(data.keys())
    placeholders = ":" + ", :".join(data.keys())
    sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"

    try:
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute(sql, data)
        conn.commit()
        return {"status": "success", "row_count": cursor.rowcount}
    except Exception as e:
        conn.rollback()
        raise HTTPException(500, str(e))
    finally:
        cursor.close()
        conn.close()
